# AI Development Guidelines & Exception Rules

## 🚫 CRITICAL MISTAKES TO AVOID

### Authentication & API Calls
- ❌ **NEVER use multiple API attempts/fallback patterns** in AuthContext
- ❌ **NEVER add hardcoded fallback URLs** (localhost:5002, devapi.shpnfinance.com)
- ❌ **NEVER import mock services** in production code
- ✅ **ALWAYS use single API_URL** from environment configuration
- ✅ **ALWAYS use configured API_URL** only

### Mock Services & Test Code
- ❌ **NEVER import mock services** in production files
- ❌ **NEVER add test pages** (TestPage, SimpleTest, AuthTest, etc.)
- ❌ **NEVER import development utilities** (log-errors, systemTest, corsTest)
- ❌ **NEVER add debugging tools** in production builds
- ✅ **ALWAYS remove mock imports** before production
- ✅ **ALWAYS clean test routes** from production

### Build & Compilation
- ❌ **NEVER force TypeScript compilation** with strict error checking in backend
- ❌ **NEVER create complex build scripts** that can fail
- ✅ **ALWAYS use runtime compilation** (ts-node) for backend
- ✅ **ALWAYS test builds** before claiming success

### File Management
- ❌ **NEVER remove essential files** without checking dependencies
- ❌ **NEVER delete files** that are imported elsewhere
- ✅ **ALWAYS check imports** before removing files
- ✅ **ALWAYS verify dependencies** before cleanup

## ✅ APPROVED PATTERNS

### Authentication Flow
```typescript
// ✅ CORRECT - Single API call
const response = await axios.post(`${API_URL}/users/login`, {
  email,
  password,
}, {
  withCredentials: true,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
});

// ❌ WRONG - Multiple attempts
// try { API_URL } catch { try { /api } catch { try { localhost:5002 } } }
```

### Service Imports
```typescript
// ✅ CORRECT - Production only
import { userSystemService } from './api';

// ❌ WRONG - Mock imports
// import mockSystemConfigService from './mockSystemConfigService';
// if (USE_MOCK) { return mockService... }
```

### Build Configuration
```json
// ✅ CORRECT - Backend package.json
{
  "build": "echo 'Build completed - using ts-node for runtime compilation'",
  "start": "node dist/index.js",
  "dev": "nodemon --exec ts-node src/index.ts"
}

// ❌ WRONG - Complex TypeScript compilation
// "build": "tsc --strict --noEmitOnError"
```

## 🔧 ENVIRONMENT CONFIGURATION

### API URLs
- ✅ **Development**: `VITE_API_URL=/api` (proxy to localhost:5000)
- ✅ **Production**: `VITE_API_URL=/api` (relative to domain)
- ❌ **Never hardcode**: `https://devapi.shpnfinance.com/api`

### Mock Configuration
- ✅ **Always disabled**: `VITE_USE_MOCK=false`
- ✅ **Remove mock checks**: No `if (USE_MOCK)` patterns
- ❌ **Never enable**: Mock services in production

## 📁 FILE STRUCTURE RULES

### Keep These Files
- ✅ All production components and pages
- ✅ Real service files (not mock)
- ✅ Essential configuration files
- ✅ User upload directories

### Remove These Files
- ❌ Test pages (TestPage.tsx, SimpleTest.tsx, etc.)
- ❌ Mock services (mockWalletManagementService.ts, etc.)
- ❌ Debug utilities (log-errors.js, systemTest.ts, etc.)
- ❌ Development scripts (test-*.js, debug-*.js)

## 🚀 DEPLOYMENT CHECKLIST

### Frontend
- [ ] No mock service imports
- [ ] No test page routes
- [ ] No development utilities
- [ ] Build completes successfully
- [ ] Uses single API_URL configuration

### Backend
- [ ] No TypeScript compilation errors blocking
- [ ] Runtime compilation with ts-node
- [ ] No hardcoded URLs
- [ ] Environment variables properly configured

## 📝 MEMORY RULES

### User Preferences (NEVER FORGET)
- User wants **NO mock services** in production
- User wants **single API calls** only (no fallbacks)
- User wants **clean production builds**
- User wants **Docker setup** with hot reload
- User wants **real authentication** only
- User wants **CRYPTO_NETWORKS** from cryptoNetworks.ts
- User wants **POST/GET wallet-management endpoints**

### Technical Decisions
- Frontend: Vite + React + TypeScript + Chakra UI
- Backend: Node.js + Express + TypeScript + MongoDB
- Authentication: JWT with cookies
- API: RESTful with /api prefix
- Build: Frontend (Vite), Backend (ts-node runtime)

## 🔄 WORKFLOW RULES

1. **Always check existing code** before making changes
2. **Always test builds** after modifications
3. **Always verify imports** before removing files
4. **Always use environment variables** for configuration
5. **Always ask user** before major architectural changes

## 🎯 SUCCESS CRITERIA

- ✅ Frontend builds without errors
- ✅ Backend runs without compilation issues
- ✅ No mock services in production
- ✅ Single API call patterns only
- ✅ Clean, maintainable codebase
- ✅ User requirements fully met

---

**Last Updated**: 2025-06-16
**Version**: 1.0
**Status**: Active Guidelines
