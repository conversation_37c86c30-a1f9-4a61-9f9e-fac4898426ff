# MongoDB Configuration
MONGO_USER=cryptoyield_admin
MONGO_PASSWORD=secure_password123

# JWT Configuration
JWT_SECRET=crypto_yield_hub_secret_key_2024

# Application Settings
NODE_ENV=production
FRONTEND_URL=http://localhost
PORT=5000
API_URL=http://localhost:5000/api

# Smart Contract Settings
CONTRACT_ADDRESS=******************************************
PROVIDER_URL=https://mainnet.infura.io/v3/your_infura_key_here
INFURA_ID=your_infura_id_here
STORAGE_KEY=crypto_yield_hub_prod_key_2024

# Mongo Express Configuration
MONGO_EXPRESS_USER=admin
MONGO_EXPRESS_PASSWORD=secure_password456
