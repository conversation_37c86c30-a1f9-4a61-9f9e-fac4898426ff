import { Request, Response } from 'express';
import WithdrawalAddress from '../models/withdrawalAddressModel';
import UserWallet from '../models/userWalletModel';
import Transaction from '../models/transactionModel';
import User from '../models/userModel';
import { catchAsync } from '../utils/catchAsync';
import { AppError } from '../utils/AppError';
import { logger } from '../utils/logger';

// @desc    Create withdrawal request using saved address
// @route   POST /api/withdrawals/create
// @access  Private
export const createWithdrawal = catchAsync(async (req: Request, res: Response) => {
  const { addressId, amount, currency, twoFactorCode } = req.body;

  // Validation
  if (!addressId || !amount || !currency) {
    throw new AppError('Address ID, amount, and currency are required', 400);
  }

  if (amount <= 0) {
    throw new AppError('Amount must be greater than 0', 400);
  }

  // Get user
  const user = await User.findById(req.user._id);
  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Verify 2FA if enabled (simplified for demo)
  if (user.twoFactorEnabled && !twoFactorCode) {
    throw new AppError('Two-factor authentication code is required', 400);
  }

  // Get withdrawal address
  const withdrawalAddress = await WithdrawalAddress.findOne({
    _id: addressId,
    userId: req.user._id,
    currency: currency.toUpperCase(),
    isActive: true,
    isVerified: true
  });

  if (!withdrawalAddress) {
    throw new AppError('Withdrawal address not found or not verified', 404);
  }

  // Get user wallet for this currency
  const userWallet = await UserWallet.findOne({
    userId: req.user._id,
    currency: currency.toUpperCase(),
    isActive: true
  });

  if (!userWallet) {
    throw new AppError('Wallet not found for this currency', 404);
  }

  // Check balance
  if (userWallet.balance < amount) {
    throw new AppError('Insufficient balance', 400);
  }

  // Calculate withdrawal fee (example: 1% or minimum 0.001)
  const feePercentage = 0.01; // 1%
  const minimumFee = 0.001;
  const calculatedFee = amount * feePercentage;
  const withdrawalFee = Math.max(calculatedFee, minimumFee);
  const netAmount = amount - withdrawalFee;

  // Check minimum withdrawal amount
  const minimumWithdrawal = getMinimumWithdrawal(currency.toUpperCase());
  if (netAmount < minimumWithdrawal) {
    throw new AppError(`Minimum withdrawal amount is ${minimumWithdrawal} ${currency}`, 400);
  }

  // Create withdrawal transaction
  const transaction = new Transaction({
    userId: req.user._id,
    walletId: userWallet._id,
    walletAddress: withdrawalAddress.address,
    type: 'withdrawal',
    asset: currency.toUpperCase(),
    amount: netAmount,
    status: 'pending',
    blockchainNetwork: withdrawalAddress.network,
    description: `Withdrawal to ${withdrawalAddress.label}`,
    metadata: {
      withdrawalAddressId: withdrawalAddress._id,
      originalAmount: amount,
      withdrawalFee: withdrawalFee,
      feePercentage: feePercentage,
      addressLabel: withdrawalAddress.label
    }
  });

  await transaction.save();

  // Update wallet balance (deduct the full amount including fee)
  userWallet.balance -= amount;
  await userWallet.save();

  // Mark withdrawal address as used
  await withdrawalAddress.markAsUsed();

  logger.info(`Withdrawal request created for user: ${req.user._id}, amount: ${amount} ${currency}, address: ${withdrawalAddress.formattedAddress}`);

  res.status(201).json({
    success: true,
    message: 'Withdrawal request created successfully',
    data: {
      transactionId: transaction._id,
      amount: netAmount,
      originalAmount: amount,
      withdrawalFee: withdrawalFee,
      currency: currency.toUpperCase(),
      address: withdrawalAddress.formattedAddress,
      addressLabel: withdrawalAddress.label,
      network: withdrawalAddress.network,
      status: transaction.status,
      estimatedProcessingTime: '1-24 hours'
    }
  });
});

// @desc    Get withdrawal history
// @route   GET /api/withdrawals/history
// @access  Private
export const getWithdrawalHistory = catchAsync(async (req: Request, res: Response) => {
  const { page = 1, limit = 10, currency, status } = req.query;

  const query: any = {
    userId: req.user._id,
    type: 'withdrawal'
  };

  if (currency) {
    query.asset = (currency as string).toUpperCase();
  }

  if (status) {
    query.status = status;
  }

  const options = {
    page: parseInt(page as string),
    limit: parseInt(limit as string),
    sort: { createdAt: -1 },
    populate: [
      {
        path: 'walletId',
        select: 'currency'
      }
    ]
  };

  const transactions = await Transaction.find(query)
    .populate(options.populate)
    .sort(options.sort)
    .limit(options.limit * options.page)
    .skip((options.page - 1) * options.limit);

  const total = await Transaction.countDocuments(query);

  const formattedTransactions = transactions.map(tx => ({
    id: tx._id,
    amount: tx.amount,
    currency: tx.asset,
    address: tx.walletAddress,
    status: tx.status,
    txHash: tx.txHash,
    network: tx.blockchainNetwork,
    description: tx.description,
    metadata: tx.metadata,
    createdAt: tx.createdAt,
    updatedAt: tx.updatedAt
  }));

  res.json({
    success: true,
    data: formattedTransactions,
    pagination: {
      page: options.page,
      limit: options.limit,
      total,
      pages: Math.ceil(total / options.limit)
    }
  });
});

// @desc    Get withdrawal limits and fees
// @route   GET /api/withdrawals/limits
// @access  Private
export const getWithdrawalLimits = catchAsync(async (req: Request, res: Response) => {
  const { currency } = req.query;

  const limits = getWithdrawalLimitsForCurrency(currency as string);

  res.json({
    success: true,
    data: limits
  });
});

// @desc    Cancel pending withdrawal
// @route   POST /api/withdrawals/:id/cancel
// @access  Private
export const cancelWithdrawal = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;

  const transaction = await Transaction.findOne({
    _id: id,
    userId: req.user._id,
    type: 'withdrawal',
    status: 'pending'
  });

  if (!transaction) {
    throw new AppError('Withdrawal not found or cannot be cancelled', 404);
  }

  // Get user wallet
  const userWallet = await UserWallet.findById(transaction.walletId);
  if (!userWallet) {
    throw new AppError('Wallet not found', 404);
  }

  // Refund the amount to wallet
  const originalAmount = transaction.metadata?.originalAmount || transaction.amount;
  userWallet.balance += originalAmount;
  await userWallet.save();

  // Update transaction status
  transaction.status = 'failed';
  transaction.description += ' (Cancelled by user)';
  await transaction.save();

  logger.info(`Withdrawal cancelled for user: ${req.user._id}, transaction: ${id}`);

  res.json({
    success: true,
    message: 'Withdrawal cancelled successfully',
    data: {
      transactionId: transaction._id,
      refundedAmount: originalAmount,
      currency: transaction.asset
    }
  });
});

// @desc    Estimate withdrawal fee
// @route   POST /api/withdrawals/estimate-fee
// @access  Private
export const estimateWithdrawalFee = catchAsync(async (req: Request, res: Response) => {
  const { amount, currency } = req.body;

  if (!amount || !currency) {
    throw new AppError('Amount and currency are required', 400);
  }

  if (amount <= 0) {
    throw new AppError('Amount must be greater than 0', 400);
  }

  // Calculate fee
  const feePercentage = 0.01; // 1%
  const minimumFee = 0.001;
  const calculatedFee = amount * feePercentage;
  const withdrawalFee = Math.max(calculatedFee, minimumFee);
  const netAmount = amount - withdrawalFee;

  // Get limits
  const limits = getWithdrawalLimitsForCurrency(currency);

  res.json({
    success: true,
    data: {
      originalAmount: amount,
      withdrawalFee: withdrawalFee,
      netAmount: netAmount,
      currency: currency.toUpperCase(),
      feePercentage: feePercentage * 100, // Convert to percentage
      limits: limits
    }
  });
});

// Helper functions
function getMinimumWithdrawal(currency: string): number {
  const minimums: { [key: string]: number } = {
    'BTC': 0.001,
    'ETH': 0.01,
    'USDT': 10,
    'BNB': 0.1,
    'ADA': 10,
    'DOT': 1,
    'LINK': 1,
    'UNI': 1
  };
  return minimums[currency] || 0.001;
}

function getWithdrawalLimitsForCurrency(currency?: string) {
  const allLimits = {
    'BTC': {
      minimum: 0.001,
      maximum: 10,
      dailyLimit: 50,
      feePercentage: 1,
      minimumFee: 0.0001,
      estimatedTime: '1-6 hours'
    },
    'ETH': {
      minimum: 0.01,
      maximum: 100,
      dailyLimit: 500,
      feePercentage: 1,
      minimumFee: 0.001,
      estimatedTime: '15-30 minutes'
    },
    'USDT': {
      minimum: 10,
      maximum: 50000,
      dailyLimit: 100000,
      feePercentage: 1,
      minimumFee: 1,
      estimatedTime: '15-30 minutes'
    },
    'BNB': {
      minimum: 0.1,
      maximum: 1000,
      dailyLimit: 5000,
      feePercentage: 1,
      minimumFee: 0.01,
      estimatedTime: '5-15 minutes'
    },
    'ADA': {
      minimum: 10,
      maximum: 10000,
      dailyLimit: 50000,
      feePercentage: 1,
      minimumFee: 1,
      estimatedTime: '5-15 minutes'
    },
    'DOT': {
      minimum: 1,
      maximum: 1000,
      dailyLimit: 5000,
      feePercentage: 1,
      minimumFee: 0.1,
      estimatedTime: '5-15 minutes'
    },
    'LINK': {
      minimum: 1,
      maximum: 1000,
      dailyLimit: 5000,
      feePercentage: 1,
      minimumFee: 0.1,
      estimatedTime: '15-30 minutes'
    },
    'UNI': {
      minimum: 1,
      maximum: 1000,
      dailyLimit: 5000,
      feePercentage: 1,
      minimumFee: 0.1,
      estimatedTime: '15-30 minutes'
    }
  };

  if (currency) {
    return allLimits[currency.toUpperCase()] || null;
  }

  return allLimits;
}
