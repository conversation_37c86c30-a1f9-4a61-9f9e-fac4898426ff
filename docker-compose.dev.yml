# Development Docker Compose Configuration
# Features:
# - MongoDB with Replica Set (supports transactions)
# - Backend with hot reload and debugging
# - Mongo Express for database management
# - Redis for caching
# - Optimized for development workflow

version: '3.8'

services:
  # MongoDB with Replica Set for Transaction Support
  mongodb:
    image: mongo:6.0
    container_name: cryptoyield-mongodb-dev
    restart: unless-stopped
    depends_on:
      mongo-keyfile-setup:
        condition: service_completed_successfully
    environment:
      MONGO_INITDB_DATABASE: cryptoyield
    volumes:
      - mongodb_dev_data:/data/db
      - ./backend/scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      cryptoyield-dev-network:
        aliases:
          - mongo
          - mongodb
    ports:
      - "27017:27017"
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 40s
    # Enable replica set for transactions support without keyfile for development
    command: ["mongod", "--bind_ip_all", "--replSet", "rs0"]

  # MongoDB Keyfile Setup
  mongo-keyfile-setup:
    image: mongo:6.0
    container_name: cryptoyield-mongo-keyfile-setup
    volumes:
      - ./backend/scripts/mongo-keyfile:/tmp/keyfile:ro
      - mongodb_dev_data:/data/db
    command: >
      bash -c "
        echo 'Setting up MongoDB keyfile...'
        cp /tmp/keyfile /data/keyfile
        chmod 600 /data/keyfile
        chown 999:999 /data/keyfile
        echo 'Keyfile setup completed'
      "
    restart: "no"

  # MongoDB Replica Set Initialization
  mongo-setup:
    image: mongo:6.0
    container_name: cryptoyield-mongo-setup-dev
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - cryptoyield-dev-network
    volumes:
      - ./backend/scripts/init-replica-set.js:/scripts/init-replica-set.js:ro
    command: >
      bash -c "
        echo 'Waiting for MongoDB to be ready...'
        sleep 10
        echo 'Initializing replica set...'
        mongosh --host mongodb:27017 --eval '
          try {
            rs.status()
            print(\"Replica set already initialized\")
          } catch(e) {
            print(\"Initializing replica set...\")
            rs.initiate({
              _id: \"rs0\",
              members: [
                { _id: 0, host: \"mongodb:27017\" }
              ]
            })
            print(\"Replica set initialized successfully\")
          }
        '
        echo 'MongoDB setup completed'
      "
    restart: "no"

  # Redis for Caching
  redis:
    image: redis:7-alpine
    container_name: cryptoyield-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - cryptoyield-dev-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Mongo Express - Database Management UI
  mongo-express:
    image: mongo-express:1.0.2
    container_name: cryptoyield-mongo-express-dev
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_SERVER: mongodb
      ME_CONFIG_MONGODB_PORT: 27017
      ME_CONFIG_MONGODB_ENABLE_ADMIN: 'false'
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
      ME_CONFIG_OPTIONS_EDITORTHEME: ambiance
      ME_CONFIG_REQUEST_SIZE: 100kb
      ME_CONFIG_SITE_BASEURL: /
    depends_on:
      mongodb:
        condition: service_healthy
      mongo-setup:
        condition: service_completed_successfully
    networks:
      - cryptoyield-dev-network
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "8081"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Backend with Hot Reload and Debugging
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
      args:
        NODE_ENV: development
    container_name: cryptoyield-backend-dev
    restart: unless-stopped
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
      mongo-setup:
        condition: service_completed_successfully
    environment:
      - NODE_ENV=development
      # MongoDB URI with replica set for transactions
      - MONGO_URI=mongodb://mongodb:27017/cryptoyield?replicaSet=rs0
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=dev_jwt_secret_key_change_in_production
      - JWT_EXPIRES_IN=1d
      - JWT_REFRESH_EXPIRES_IN=7d
      - PORT=5000
      - FRONTEND_URL=http://localhost:3000
      - CONTRACT_ADDRESS=******************************************
      - PROVIDER_URL=https://mainnet.infura.io/v3/********************************
      - CORS_ORIGIN=*
      - LOG_LEVEL=debug
      - RATE_LIMIT_WINDOW_MS=60000
      - RATE_LIMIT_MAX=100
      - CACHE_TTL=300
      # Crypto Configuration
      - SUPPORTED_CURRENCIES=BTC,ETH,USDT,BNB,SOL,DOGE,TRX
      - MASTER_SEED_PHRASE=abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about
      # Enable hot reload
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    ports:
      - "5000:5000"
      - "9229:9229"  # Debug port for VS Code debugging
    volumes:
      # Hot reload: Mount source code for live changes
      - ./backend/src:/app/src:cached
      - ./backend/package.json:/app/package.json:ro
      - ./backend/package-lock.json:/app/package-lock.json:ro
      - ./backend/tsconfig.json:/app/tsconfig.json:ro
      - ./backend/nodemon.json:/app/nodemon.json:ro
      - ./backend/.env.docker:/app/.env.docker:ro
      - ./backend/scripts:/app/scripts:ro
      # Persistent uploads directory
      - uploads_dev_data:/app/uploads
      # Exclude node_modules from host (use container's node_modules)
      - /app/node_modules
    networks:
      - cryptoyield-dev-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # Use nodemon for hot reload in development
    command: ["npm", "run", "dev:docker"]
    # Enable debugging and interactive mode
    stdin_open: true
    tty: true

networks:
  cryptoyield-dev-network:
    driver: bridge

volumes:
  mongodb_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  uploads_dev_data:
    driver: local
