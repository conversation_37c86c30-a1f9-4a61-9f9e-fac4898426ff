# CryptoYield Frontend Development Environment Variables

# API Configuration
VITE_API_URL=https://devapi.shpnfinance.com/api
VITE_SOCKET_URL=https://devapi.shpnfinance.com

# Application Configuration
VITE_APP_NAME=CryptoYield
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Cryptocurrency Investment Platform

# Blockchain Configuration
VITE_CONTRACT_ADDRESS=******************************************
VITE_PROVIDER_URL=https://mainnet.infura.io/v3/********************************

# Development Settings
VITE_DEV_MODE=true
VITE_DEBUG=true
VITE_LOG_LEVEL=debug

# Hot Reload Settings
CHOKIDAR_USEPOLLING=true
WATCHPACK_POLLING=true

# Build Configuration
VITE_BUILD_MODE=development
VITE_SOURCEMAP=true

# Feature Flags
VITE_ENABLE_MOCK_DATA=false
VITE_ENABLE_DEV_TOOLS=true
VITE_ENABLE_CONSOLE_LOGS=true

# Security (Development Only)
VITE_DISABLE_SSL_VERIFY=true
VITE_ALLOW_HTTP=true
