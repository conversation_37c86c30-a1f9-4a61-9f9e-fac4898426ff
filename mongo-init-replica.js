// MongoDB Replica Set Initialization Script
// This script initializes a single-node replica set for MongoDB

print('Starting replica set initialization...');

// Wait for MongoDB to be ready
sleep(1000);

try {
  // Check if replica set is already initialized
  var status = rs.status();
  print('Replica set already initialized:', status.set);
} catch (e) {
  print('Initializing replica set...');
  
  // Initialize replica set with single node
  var config = {
    _id: 'rs0',
    members: [
      {
        _id: 0,
        host: 'localhost:27017',
        priority: 1
      }
    ]
  };
  
  var result = rs.initiate(config);
  print('Replica set initialization result:', result);
  
  // Wait for replica set to be ready
  sleep(2000);
  
  // Check status
  var finalStatus = rs.status();
  print('Final replica set status:', finalStatus.ok);
}

print('Replica set initialization completed.');
